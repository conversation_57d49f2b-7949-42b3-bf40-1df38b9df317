version: '3.9'

services:
  teable:
    image: ghcr.io/teableio/teable:latest
    deploy:
      replicas: 2
    restart: always
    expose:
      - '3000'
    volumes:
      - ./apps/nextjs-app:/app/apps/nextjs-app:ro
      - ./apps/nestjs-backend:/app/apps/nestjs-backend:ro
      - ./packages:/app/packages:ro
      - ./plugins:/app/plugins:ro
      - ./scripts:/app/scripts:ro
      - teable-data:/app/.assets:rw
      - teable-temp:/app/.temporary:rw
    env_file:
      - .env
    environment:
      - TZ=${TIMEZONE}
      - NEXT_ENV_IMAGES_ALL_REMOTE=true
    networks:
      - teable-network
    depends_on:
      teable-cache:
        condition: service_healthy
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3000/health']
      start_period: 5s
      interval: 5s
      timeout: 3s
      retries: 3

  teable-db:
    image: postgres:15.4
    restart: always
    ports:
      - '42345:5432'
    volumes:
      - teable-db:/var/lib/postgresql/data:rw
    environment:
      - TZ=${TIMEZONE}
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    networks:
      - teable-network
    healthcheck:
      test: ['CMD-SHELL', "sh -c 'pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}'"]
      interval: 10s
      timeout: 3s
      retries: 3

  teable-cache:
    image: redis:7.2.4
    restart: always
    expose:
      - '6379'
    volumes:
      - teable-cache:/data:rw
    networks:
      - teable-network
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    healthcheck:
      test: ['CMD', 'redis-cli', '--raw', 'incr', 'ping']
      interval: 10s
      timeout: 3s
      retries: 3

  teable-storage:
    image: minio/minio:RELEASE.2024-02-17T01-15-57Z
    expose:
      - '9000'
      - '9001'
    environment:
      - MINIO_SERVER_URL=${MINIO_SERVER_URL}
      - MINIO_BROWSER_REDIRECT_URL=${MINIO_BROWSER_REDIRECT_URL}
      - MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY}
      - MINIO_SECRET_KEY=${MINIO_SECRET_KEY}
    volumes:
      - teable-storage:/data:rw
    networks:
      - teable-network
    command: server /data --console-address ":9001"

  createbuckets:
    image: minio/mc
    networks:
      - teable-network
    entrypoint: >
      /bin/sh -c "
      /usr/bin/mc alias set teable-storage http://teable-storage:9000 ${MINIO_ACCESS_KEY} ${MINIO_SECRET_KEY};
      /usr/bin/mc mb teable-storage/public;
      /usr/bin/mc anonymous set public teable-storage/public;
      /usr/bin/mc mb teable-storage/private;
      exit 0;
      "
    depends_on:
      teable-storage:
        condition: service_started

  teable-gateway:
    image: openresty/openresty:********-2-bookworm-fat
    restart: unless-stopped
    ports:
      - '80:80'
      - '443:443'
      - '9000:9000'
    volumes:
      - './gateway/conf.d:/etc/nginx/conf.d'
    networks:
      - teable-network
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost/healthcheck']
      interval: 10s
      timeout: 3s
      retries: 3
    depends_on:
      teable:
        condition: service_started

networks:
  teable-network:
    name: teable-network
    driver: bridge

volumes:
  teable-db: {}
  teable-cache: {}
  teable-storage: {}
  teable-data: {}
  teable-temp: {}